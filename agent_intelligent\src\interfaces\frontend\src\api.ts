import axios from 'axios';

const API_BASE = 'http://localhost:8000';

export async function generateNewsletter(theme: string, description: string): Promise<string> {
  const res = await axios.post(`${API_BASE}/generate`, {
    theme,
    description,
  });
  return res.data.newsletter;
}

export async function reviseNewsletter(feedback: string): Promise<string> {
  const res = await axios.post(`${API_BASE}/revise`, {
    feedback,
  });
  return res.data.revised_newsletter;
}
