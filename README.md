# Agent Intelligent pour la Génération Automatisée de Newsletters
## Description
Ce projet propose un workflow automatisé complet pour la génération de newsletters à partir de diverses sources d'information (sites web, flux RSS, réseaux sociaux...). Il s’appuie sur des technologies d’IA, de traitement du langage naturel (TALN) et des outils de scraping, de résumé automatique et de génération HTML.

## Fonctionnalités principales
- Collecte de contenu : scraping de sites web, bases de données internes, réseaux sociaux.

- Nettoyage & prétraitement : suppression des balises HTML, normalisation du texte, détection de langue.

- Résumé automatique : généré par des modèles de type GPT, BART, T5.

- Génération de newsletters : titres, résumés, mise en page HTML responsive.

- Diffusion : intégration aux plateformes comme Mailchimp ou Sendinblue via API.

- Intervention humaine optionnelle pour relecture et enrichissement du contenu.

## Technologies possible utilisées
- Scraping : BeautifulSoup, Newspaper3k, feedparser

- TALN & résumé : spaCy, NLTK, langdetect, Hugging Face Transformers, OpenAI API, LangChain

- Génération HTML : Jinja2, Markdown-to-HTML, HTML/CSS

- Diffusion : SMTP, Mailchimp API, Sendinblue API

- Orchestration : LangChain

## Pipeline général
- Collecte de contenus à partir de sources variées

- Prétraitement des textes

- Résumé automatique (abstractive)

- Génération HTML de la newsletter

- Diffusion via plateforme emailing

- Validation humaine
