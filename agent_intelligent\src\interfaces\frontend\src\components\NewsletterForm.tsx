import { useState, FormEvent } from "react";
import { generateNewsletter } from "../api";

// Props optionnelles, tu peux supprimer si inutilisé ailleurs
interface Props {
  onGenerated: (newsletter: string) => void;
}

export default function NewsletterForm({ onGenerated }: Props) {
  // Champs du formulaire
  const [theme, setTheme] = useState("");
  const [description, setDescription] = useState("");

  // États de chargement et contenu
  const [loading, setLoading] = useState(false);
  const [newsletter, setNewsletter] = useState("");
  const [feedback, setFeedback] = useState("");
  const [revisedNewsletter, setRevisedNewsletter] = useState("");

  // 🔄 Soumission du formulaire
  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    if (!theme.trim()) {
      alert("Veuillez entrer un thème.");
      return;
    }

    setLoading(true);
    try {
      const result = await generateNewsletter(theme, description);
      setNewsletter(result);           // Stocker la newsletter générée
      setRevisedNewsletter("");        // Réinitialiser les anciennes révisions
      setFeedback("");                 // Réinitialiser le feedback utilisateur
    //   onGenerated(result);             // Peut être ignoré si non utilisé
    } catch (error) {
      console.error("Erreur lors de la génération :", error);
      alert("Une erreur est survenue. Vérifie ton backend.");
    } finally {
      setLoading(false);
    }
  };

  // ✏️ Demande de révision
  const handleRevise = async () => {
    if (!feedback.trim()) {
      alert("Merci d'écrire une remarque !");
      return;
    }

    try {
      const res = await fetch("http://localhost:8000/revise", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ feedback }),
      });

      if (!res.ok) {
        throw new Error("Erreur lors de la révision");
      }

      const data = await res.json();
      setRevisedNewsletter(data.revised_newsletter);
    } catch (err) {
      alert("⚠️ Impossible de réviser la newsletter.");
      console.error(err);
    }
  };

  // 📋 Copier dans le presse-papiers
  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      alert("📋 Newsletter copiée !");
    });
  };

  // 📝 Gestion des champs
  const handleInputChange = (
    setter: React.Dispatch<React.SetStateAction<string>>
  ) => (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setter(e.target.value);
    setRevisedNewsletter(""); // Annule toute ancienne révision si du texte change
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* 🎯 Champ Thème */}
      <div>
        <label className="block font-semibold">🌟 Thème</label>
        <input
          type="text"
          placeholder="Ex : thème"
          value={theme}
          onChange={handleInputChange(setTheme)}
          className="w-full p-2 border border-gray-300 rounded mt-1"
        />
      </div>

      {/* 📝 Champ Description */}
      <div>
        <label className="block font-semibold">📝 Description</label>
        <textarea
          placeholder="Décrivez l'objectif ou le sujet"
          value={description}
          onChange={handleInputChange(setDescription)}
          className="w-full p-2 border border-gray-300 rounded mt-1"
        />
      </div>

      {/* 🚀 Bouton Générer */}
      <button
        type="submit"
        className="w-full bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
        disabled={loading}
      >
        {loading ? "Génération en cours..." : "✨ Générer la newsletter"}
      </button>

      {/* ✅ Affichage de la newsletter originale (si pas encore révisée) */}
      {newsletter && !revisedNewsletter && (
        <div className="mt-6">
          <h2 className="text-xl font-bold mb-2">📩 Newsletter générée</h2>
          <pre className="bg-gray-100 p-4 rounded whitespace-pre-wrap">
            {newsletter}
          </pre>
          <button
            type="button"
            onClick={() => copyToClipboard(newsletter)}
            className="mt-2 bg-gray-800 text-white px-3 py-1 rounded hover:bg-gray-700"
          >
            📋 Copier
          </button>

          {/* ✍️ Section pour saisir une remarque */}
          <div className="mt-6">
            <h3 className="font-semibold mb-1">✏️ Remarque de l'utilisateur</h3>
            <textarea
              placeholder="Exemple : Peux-tu ajouter un ton plus amical ?"
              value={feedback}
              onChange={handleInputChange(setFeedback)}
              className="w-full p-2 border border-gray-300 rounded mt-1"
            />
            <button
              type="button"
              onClick={handleRevise}
              className="mt-2 bg-purple-600 text-white px-4 py-2 rounded hover:bg-purple-700 transition"
            >
              🔁 Réviser la newsletter
            </button>
          </div>
        </div>
      )}

      {/* 🧠 Affichage de la version révisée */}
      {revisedNewsletter && (
        <div className="mt-6">
          <h2 className="text-xl font-bold mb-2">🎯 Newsletter révisée</h2>
          <pre className="bg-green-100 p-4 rounded whitespace-pre-wrap">
            {revisedNewsletter}
          </pre>
          <button
            type="button"
            onClick={() => copyToClipboard(revisedNewsletter)}
            className="mt-2 bg-green-700 text-white px-3 py-1 rounded hover:bg-green-800"
          >
            📋 Copier
          </button>
        </div>
      )}
    </form>
  );
}
