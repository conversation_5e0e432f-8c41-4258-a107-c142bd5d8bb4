import sys
import os
import time
import asyncio
import streamlit as st

# Autoriser certaines extensions Torch
os.environ["TORCH_CLASS_EXTENSION_ALLOW_LIST"] = "True"

# Ajouter la racine du projet au chemin Python
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

try:
    asyncio.get_running_loop()
except RuntimeError:
    asyncio.set_event_loop(asyncio.new_event_loop())

# Imports internes
from src.agent.scraping import scrape_content
from models.compare_models import summarize_with_all_models


# Titre
st.title("📰 Générateur de Newsletter + Comparateur de Modèles")

# Entrée utilisateur
theme = st.text_input("🎯 Thème de la newsletter", placeholder="ex : Intelligence Artificielle")
description = st.text_area("🧾 Description courte", placeholder="Décrivez brièvement l'objectif ou le contenu...")

# Bouton de génération
if st.button("✨ Générer le contenu"):
    if not theme.strip():
        st.warning("⚠️ Veuillez entrer un thème.")
    else:
        st.subheader("🔍 Résultats du scraping")
        st.markdown(f"**🎯 Thème choisi :** `{theme}`")
        st.markdown(f"**🧾 Description :** `{description}`")

        with st.spinner("🔄 Scraping en cours..."):
            scraped_data = scrape_content(theme, description)

        if scraped_data:
            for item in scraped_data:
                st.markdown(f"- {item}")

            combined_content = "\n".join(scraped_data)

            st.subheader("🧠 Résumé synthétique généré par BART")
            with st.spinner("✍️ Résumé en cours..."):
                result, chunks, partials, sub_partials = summary(combined_content, return_diagnostics=True)
                st.markdown(f"✅ **Résumé final (BART)** :\n\n> {result}")

                with st.expander("📦 Détails techniques du résumé"):
                    st.write(f"🧩 Chunks initiaux : {len(chunks)}")
                    st.write(f"✂️ Résumés partiels : {len(partials)}")
                    if sub_partials:
                        st.write(f"🔁 Méta-chunks : {len(sub_partials)}")
                    st.subheader("🧾 Résumés intermédiaires :")
                    for i, part in enumerate(partials):
                        st.markdown(f"**Résumé {i+1} :** {part}")
                    if sub_partials:
                        st.subheader("🔄 Résumés après regroupement :")
                        for i, meta in enumerate(sub_partials):
                            st.markdown(f"**Synthèse {i+1} :** {meta}")

            # Comparaison automatique
            st.subheader("📊 Comparaison automatique des 3 modèles (sans référence)")
            with st.spinner("🤖 Génération des résumés avec tous les modèles..."):
                start = time.time()
                model_summaries = summarize_with_all_models(combined_content)
                end = time.time()

            st.write(f"⏱️ Temps total de génération : {round(end - start, 2)}s")

            for model_name, summary_text in model_summaries.items():
                words = len(summary_text.split())
                compression = round((words / len(combined_content.split())) * 100, 2)

                st.markdown(f"### 🧠 {model_name}")
                st.write(f"🔢 **Longueur du résumé** : {words} mots")
                st.write(f"📉 **Taux de compression** : {compression}%")
                st.markdown(f"> {summary_text}")

            # Classement automatique
            sorted_models = sorted(model_summaries.items(), key=lambda x: len(x[1].split()))
            st.subheader("🏆 Classement automatique par concision")
            for i, (name, text) in enumerate(sorted_models, 1):
                st.write(f"{i}. **{name}** - {len(text.split())} mots")

            # Vote manuel
            st.subheader("🗳️ Votre avis : Quel résumé est le meilleur ?")
            selected = st.radio("Choisissez le résumé le plus pertinent :", list(model_summaries.keys()))

            if selected:
                st.success(f"✅ Vous avez voté pour : **{selected}**")
                st.markdown(f"💬 N'hésitez pas à commenter votre choix ci-dessous 👇")
                feedback = st.text_area("🗨️ Votre commentaire (optionnel)")

        else:
            st.info("Aucun contenu pertinent trouvé pour le thème et la description donnés.")
