import { useState } from "react";
import NewsletterForm from "./components/NewsletterForm";
import NewsletterViewer from "./components/NewsletterViewer";

function App() {
  const [newsletter, setNewsletter] = useState("");

  return (
    <div className="min-h-screen bg-gray-100 text-gray-900 p-6">
      <div className="max-w-3xl mx-auto bg-white rounded-lg shadow-lg p-8">
        <h1 className="text-3xl font-bold mb-8 text-center flex items-center justify-center gap-2">
          <span role="img" aria-label="newsletter">📰</span>
          Générateur de Newsletter
        </h1>

        <NewsletterForm onGenerated={setNewsletter} />

        {newsletter && (
          <NewsletterViewer
            title="📩 Newsletter générée"
            content={newsletter}
          />
        )}
      </div>
    </div>
  );
}

export default App;
