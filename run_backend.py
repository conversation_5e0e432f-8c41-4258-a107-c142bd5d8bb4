#!/usr/bin/env python3
"""
Script pour lancer le serveur backend FastAPI
"""

import uvicorn
import sys
import os

# Ajouter le répertoire racine au path
sys.path.append(os.path.abspath('.'))

if __name__ == "__main__":
    print("🚀 Lancement du serveur backend FastAPI...")
    print("📍 URL: http://localhost:8000")
    print("📖 Documentation API: http://localhost:8000/docs")
    print("⏹️  Pour arrêter: Ctrl+C")
    print("-" * 50)
    
    uvicorn.run(
        "agent_intelligent.src.interfaces.backend:app",
        host="0.0.0.0",
        port=8000,
        reload=True,
        log_level="info"
    )
