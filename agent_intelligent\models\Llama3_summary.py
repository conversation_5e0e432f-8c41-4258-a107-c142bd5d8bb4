
import os
import requests
from dotenv import load_dotenv
from langchain.text_splitter import RecursiveCharacterTextSplitter
from langchain.docstore.document import Document
from src.agent.preprocessing import clean_text
from prompts.summary_prompt import get_prompt

# Chargement des variables d'environnement (.env)
load_dotenv()
GROQ_API_KEY = os.getenv("GROQ_API_KEY")

if not GROQ_API_KEY:
    raise RuntimeError("❌ GROQ_API_KEY non trouvé")

# 📌 Fonction de résumé via API Groq
def generate_summary(text, max_tokens=150):
    url = "https://api.groq.com/openai/v1/chat/completions"
    headers = {
        "Authorization": f"Bearer {GROQ_API_KEY}",
        "Content-Type": "application/json"
    }
    payload = {
        "model": "llama3-8b-8192",
        "messages": [
            {
                "role": "user",
                "content": get_prompt(text)
            }
        ],
        "temperature": 0.7,
        "max_tokens": max_tokens
    }

    try:
        response = requests.post(url, headers=headers, json=payload, timeout=60)
        response.raise_for_status()
        return response.json()["choices"][0]["message"]["content"].strip()
    except Exception as e:
        return f"[❌ Erreur Groq] {e}"

# 🧠 Fonction principale avec découpage
def summarize(text):
    if not text or len(text.strip()) < 30:
        return "⛔ Contenu vide ou trop court pour résumer."

    cleaned_text = clean_text(text)
    documents = [Document(page_content=cleaned_text)]

    splitter = RecursiveCharacterTextSplitter(chunk_size=1024, chunk_overlap=100)
    split_docs = splitter.split_documents(documents)

    if not split_docs:
        return "⛔ Aucun segment valide à résumer."

    partial_summaries = []
    for i, doc in enumerate(split_docs):
        try:
            summary = generate_summary(doc.page_content)
            partial_summaries.append(summary)
        except Exception as e:
            partial_summaries.append(f"[Erreur chunk {i+1}] {str(e)}")

    return " ".join(partial_summaries).strip()

















# llama 3.8b from huggingface ---------------------------------------------------------------------



# from langchain.text_splitter import RecursiveCharacterTextSplitter
# from langchain.docstore.document import Document
# from transformers import AutoTokenizer, AutoModelForCausalLM
# import torch
# from src.agent.preprocessing import clean_text
# from huggingface_hub import login
# login("*************************************")  # mets ton token Hugging Face ici

# import os

# os.environ["HF_HUB_READ_TIMEOUT"] = "60"
# os.environ["HF_HUB_CONNECT_TIMEOUT"] = "60"
# # Choisis ton modèle ici :
# #model_name = "meta-llama/Meta-Llama-3-8B-Instruct"  # ⬅️ excellent, mais gros
# #model_name = "mistralai/Mistral-7B-Instruct-v0.2"    # ⬅️ plus léger, très bon
# #model_name = "tiiuae/falcon-7b-instruct"


# model_name = "meta-llama/Meta-Llama-3-8B-Instruct"

# tokenizer = AutoTokenizer.from_pretrained(model_name, use_fast=True)
# model = AutoModelForCausalLM.from_pretrained(
#     model_name,
#     torch_dtype=torch.float32  # CPU only
# )


# # 📌 Fonction de résumé via prompt instruct
# def generate_summary(prompt, max_new_tokens=256):
#     inputs = tokenizer(prompt, return_tensors="pt").to("cpu")

#     output = model.generate(
#         **inputs,
#         max_new_tokens=max_new_tokens,
#         do_sample=False,
#         # temperature=0.7,
#         # top_p=0.9
#     )
#     # result = tokenizer.decode(output[0], skip_special_tokens=True)
#     result = tokenizer.batch_decode(output, skip_special_tokens=True)[0].strip()

#     return result.replace(prompt, "").strip()


# # 🧠 Fonction principale avec découpage
# def summarize(text):
#     if not text or len(text.strip()) < 30:
#         return "⛔ Contenu vide ou trop court pour résumer."

#     cleaned_text = clean_text(text)
#     documents = [Document(page_content=cleaned_text)]

#     splitter = RecursiveCharacterTextSplitter(chunk_size=1024, chunk_overlap=100)
#     split_docs = splitter.split_documents(documents)

#     if not split_docs:
#         return "⛔ Aucun segment valide à résumer."

#     partial_summaries = []

#      for i, doc in enumerate(split_docs):
#         try:
#             prompt = (
#                 "<|begin_of_text|><|start_header_id|>user<|end_header_id|>\n"
#                 f"Résume ce texte en français de manière concise :\n\n{doc.page_content}\n"
#                 "<|start_header_id|>assistant<|end_header_id|>\n"
#             )
#             summary = generate_summary(prompt)
#             partial_summaries.append(summary)
#         except Exception as e:
#             partial_summaries.append(f"[Erreur sur le chunk {i+1}] {str(e)}")
 

#     final_summary = " ".join(partial_summaries).strip()
#     return final_summary
