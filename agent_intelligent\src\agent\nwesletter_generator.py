import os
from prompts.generator_prompt import get_prompt
from models.gemini_model import gemini__model

# ✉️ Fonction principale
def generate_newsletter_from_summary(summary: str, theme: str = "Actualité") -> str:
    prompt = get_prompt(summary, theme)
    try:
        response = gemini__model.generate_content(prompt)
        return response.text.strip()
    except Exception as e:
        return f"[ Erreur Gemini] {e}"
