interface Props {
    content: string;
    title?: string;
  }
  
  export default function NewsletterViewer({ content, title }: Props) {
    if (!content) return null;
  
    return (
      <div className="border border-gray-300 rounded p-4 mt-6 bg-white shadow-sm">
        {title && <h2 className="text-xl font-semibold mb-2">{title}</h2>}
        <pre className="whitespace-pre-wrap text-gray-800">{content}</pre>
      </div>
    );
  }
  